"use client";
import React, { useState, useRef, useEffect } from "react";
import { uploadDocument } from "../../../../requests/dashboardRequest";
import { compressImage, validateImageFile, formatFileSize } from "../../../../utils/imageUtils";
import toast from "react-hot-toast";

interface AuthData {
  birthDate: string;
  acceptedTerms: boolean;
  nationalCardFrontImage: string | null;
  nationalCardBackImage: string | null;
  selfieImage: string | null;
  consentAccepted: boolean;
  signature: string | null;
  consentPdf: string | null;
  uploadStatus: {
    id: boolean;
    id_back: boolean;
    selfie: boolean;
    consent: boolean;
  };
  uploadProgress: {
    id: number;
    id_back: number;
    selfie: number;
    consent: number;
  };
}

interface Props {
  authData: AuthData;
  updateAuthData: (data: Partial<AuthData>) => void;
  onNext: () => void;
  onBack: () => void;
  isFirstStep: boolean;
  isLastStep: boolean;
  canProceedToNext: boolean;
}

const NationalCardStep: React.FC<Props> = ({
  authData,
  updateAuthData,
  onNext,
  onBack,
}) => {
  const [isMobile, setIsMobile] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [dragActiveFront, setDragActiveFront] = useState(false);
  const [dragActiveBack, setDragActiveBack] = useState(false);
  const frontFileInputRef = useRef<HTMLInputElement>(null);
  const backFileInputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(/iPhone|iPad|iPod|Android/i.test(navigator.userAgent));
    };
    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, []);

  const handleFileUpload = async (file: File, side: 'front' | 'back') => {
    if (!file) return;

    // Validate file
    const validation = validateImageFile(file, 10); // 10MB max
    if (!validation.isValid) {
      toast.error(validation.error || 'فایل نامعتبر است');
      return;
    }

    try {
      // Show original file size
      console.log(`Original file size: ${formatFileSize(file.size)}`);

      // Compress image
      const compressedFile = await compressImage(file, 1200, 1200, 0.8);
      console.log(`Compressed file size: ${formatFileSize(compressedFile.size)}`);

      // Create preview URL
      const imageUrl = URL.createObjectURL(compressedFile);

      // Update UI immediately
      if (side === 'front') {
        updateAuthData({
          nationalCardFrontImage: imageUrl,
          uploadProgress: { ...authData.uploadProgress, id: 0 }
        });
      } else {
        updateAuthData({
          nationalCardBackImage: imageUrl,
          uploadProgress: { ...authData.uploadProgress, id_back: 0 }
        });
      }

      // Upload to server
      const documentName = side === 'front' ? 'id' : 'id_back';

      // Simulate progress
      let currentProgress = 0;
      const progressInterval = setInterval(() => {
        currentProgress = Math.min(currentProgress + 10, 90);
        updateAuthData({
          uploadProgress: {
            ...authData.uploadProgress,
            [documentName]: currentProgress
          }
        });
      }, 100);

      const result = await uploadDocument(compressedFile, documentName);

      clearInterval(progressInterval);

      if (result.isError) {
        toast.error(result.message || 'خطا در آپلود فایل');
        // Reset on error
        if (side === 'front') {
          updateAuthData({
            nationalCardFrontImage: null,
            uploadProgress: { ...authData.uploadProgress, id: 0 }
          });
        } else {
          updateAuthData({
            nationalCardBackImage: null,
            uploadProgress: { ...authData.uploadProgress, id_back: 0 }
          });
        }
      } else {
        toast.success('فایل با موفقیت آپلود شد');
        // Mark as uploaded
        updateAuthData({
          uploadStatus: {
            ...authData.uploadStatus,
            [documentName]: true
          },
          uploadProgress: {
            ...authData.uploadProgress,
            [documentName]: 100
          }
        });
      }
    } catch (error) {
      console.error("خطا در آپلود:", error);
      toast.error("خطا در آپلود فایل. لطفا دوباره تلاش کنید.");

      // Reset on error
      if (side === 'front') {
        updateAuthData({
          nationalCardFrontImage: null,
          uploadProgress: { ...authData.uploadProgress, id: 0 }
        });
      } else {
        updateAuthData({
          nationalCardBackImage: null,
          uploadProgress: { ...authData.uploadProgress, id_back: 0 }
        });
      }
    }
  };

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>, side: 'front' | 'back') => {
    const file = event.target.files?.[0];
    if (file) {
      handleFileUpload(file, side);
    }
  };

  const handleCameraCapture = (side: 'front' | 'back') => {
    if (isMobile) {
      if (side === 'front') {
        frontFileInputRef.current?.click();
      } else {
        backFileInputRef.current?.click();
      }
    } else {
      setShowModal(true);
    }
  };

  const handleUploadClick = (side: 'front' | 'back') => {
    if (side === 'front') {
      frontFileInputRef.current?.click();
    } else {
      backFileInputRef.current?.click();
    }
  };

  const handleDrag = (e: React.DragEvent, side: 'front' | 'back') => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      if (side === 'front') {
        setDragActiveFront(true);
      } else {
        setDragActiveBack(true);
      }
    } else if (e.type === "dragleave") {
      if (side === 'front') {
        setDragActiveFront(false);
      } else {
        setDragActiveBack(false);
      }
    }
  };

  const handleDrop = (e: React.DragEvent, side: 'front' | 'back') => {
    e.preventDefault();
    e.stopPropagation();
    if (side === 'front') {
      setDragActiveFront(false);
    } else {
      setDragActiveBack(false);
    }

    const file = e.dataTransfer.files?.[0];
    if (file && file.type.startsWith("image/")) {
      handleFileUpload(file, side);
    }
  };

  const handleNext = () => {
    if (!authData.nationalCardFrontImage) {
      toast.error("لطفا تصویر جلوی کارت ملی خود را آپلود کنید");
      return;
    }
    if (!authData.nationalCardBackImage) {
      toast.error("لطفا تصویر پشت کارت ملی خود را آپلود کنید");
      return;
    }
    if (!authData.uploadStatus.id) {
      toast.error("لطفا منتظر بمانید تا آپلود جلوی کارت ملی کامل شود");
      return;
    }
    if (!authData.uploadStatus.id_back) {
      toast.error("لطفا منتظر بمانید تا آپلود پشت کارت ملی کامل شود");
      return;
    }
    onNext();
  };

  const Modal = () => (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-[#1E1E1E] p-6 rounded-lg shadow-xl max-w-md w-full">
        <div className="text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-[#4899EB]/20 rounded-full flex items-center justify-center">
            <svg className="w-8 h-8 text-[#4899EB]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold text-white mb-3">استفاده از دوربین</h3>
          <p className="text-white/80 text-center mb-6 leading-6">
            برای استفاده از دوربین و عکس‌برداری مستقیم از کارت ملی، لطفا با تلفن همراه خود وارد سایت شوید.
            در غیر این صورت می‌توانید از گزینه "انتخاب فایل" استفاده کنید.
          </p>
          <button
            onClick={() => setShowModal(false)}
            className="w-full bg-[#4899EB] text-white py-3 rounded-lg hover:bg-blue-600 transition-colors font-medium"
          >
            متوجه شدم
          </button>
        </div>
      </div>
    </div>
  );

  return (
    <div className="max-w-2xl mx-auto">
      <div className="bg-[#18191D] rounded-lg p-8">
        <div className="text-center mb-8">
          <h2 className="text-2xl font-bold mb-2">ارسال کارت ملی</h2>
          <p className="text-gray-400">
            لطفا تصویر واضح و خوانا از کارت ملی خود ارسال کنید
          </p>
        </div>

        {/* Instructions */}
        <div className="bg-[#2A2D35] p-4 rounded-lg mb-6 border-r-4 border-r-[#4899EB]">
          <h4 className="text-white font-semibold mb-2 text-sm">نکات مهم:</h4>
          <ul className="text-white/80 text-sm space-y-1">
            <li>• تصویر کارت ملی باید واضح و خوانا باشد</li>
            <li>• تمام اطلاعات روی کارت باید قابل مشاهده باشد</li>
            <li>• از نور مناسب برای عکس‌برداری استفاده کنید</li>
            <li>• کارت ملی باید معتبر و غیر منقضی باشد</li>
          </ul>
        </div>

        {/* Upload Areas */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
          {/* Front Side */}
          <div>
            <h3 className="text-white font-semibold mb-3 text-center">جلوی کارت ملی</h3>
            <div
              className={`relative min-h-[280px] flex flex-col items-center justify-center rounded-lg border-2 border-dashed p-4 transition-all ${
                dragActiveFront
                  ? "border-[#4899EB] bg-[#4899EB]/10"
                  : authData.nationalCardFrontImage
                  ? "border-green-500 bg-green-500/5"
                  : "border-gray-600 bg-[#2A2D35]/30"
              }`}
              onDragEnter={(e) => handleDrag(e, 'front')}
              onDragLeave={(e) => handleDrag(e, 'front')}
              onDragOver={(e) => handleDrag(e, 'front')}
              onDrop={(e) => handleDrop(e, 'front')}
            >
              {authData.nationalCardFrontImage ? (
                <div className="flex flex-col items-center">
                  <img
                    src={authData.nationalCardFrontImage}
                    alt="جلوی کارت ملی"
                    className="max-h-[200px] object-contain rounded-lg shadow-lg mb-3"
                  />

                  {/* Upload Progress */}
                  {authData.uploadProgress.id < 100 && (
                    <div className="w-full mb-3">
                      <div className="flex justify-between text-xs text-gray-400 mb-1">
                        <span>در حال آپلود...</span>
                        <span>{authData.uploadProgress.id}%</span>
                      </div>
                      <div className="w-full bg-gray-700 rounded-full h-2">
                        <div
                          className="bg-[#4899EB] h-2 rounded-full transition-all duration-300"
                          style={{ width: `${authData.uploadProgress.id}%` }}
                        ></div>
                      </div>
                    </div>
                  )}

                  {/* Upload Status */}
                  {authData.uploadStatus.id ? (
                    <p className="text-green-400 text-sm flex items-center mb-2">
                      <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      آپلود موفق
                    </p>
                  ) : (
                    <p className="text-yellow-400 text-sm flex items-center mb-2">
                      <svg className="w-4 h-4 mr-2 animate-spin" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      در حال آپلود
                    </p>
                  )}

                  <button
                    onClick={() => updateAuthData({
                      nationalCardFrontImage: null,
                      uploadStatus: { ...authData.uploadStatus, id: false },
                      uploadProgress: { ...authData.uploadProgress, id: 0 }
                    })}
                    className="text-red-400 text-xs hover:text-red-300 transition-colors"
                    disabled={authData.uploadProgress.id > 0 && authData.uploadProgress.id < 100}
                  >
                    حذف و آپلود مجدد
                  </button>
                </div>
              ) : (
                <div className="text-center">
                  <div className="w-12 h-12 mx-auto mb-3 bg-[#4899EB]/20 rounded-full flex items-center justify-center">
                    <svg className="w-6 h-6 text-[#4899EB]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                    </svg>
                  </div>
                  <p className="text-white text-sm font-semibold mb-2">جلوی کارت ملی</p>
                  <p className="text-white/70 text-xs text-center mb-4">
                    تصویر جلوی کارت ملی را آپلود کنید
                  </p>
                  <div className="flex flex-col gap-2">
                    <button
                      onClick={() => handleUploadClick('front')}
                      className="flex items-center justify-center px-3 py-2 bg-[#4899EB] hover:bg-blue-600 rounded text-xs transition-colors"
                    >
                      انتخاب فایل
                    </button>
                    <button
                      onClick={() => handleCameraCapture('front')}
                      className="flex items-center justify-center px-3 py-2 bg-[#2A2D35] hover:bg-gray-600 border border-gray-600 rounded text-xs transition-colors"
                    >
                      دوربین
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Back Side */}
          <div>
            <h3 className="text-white font-semibold mb-3 text-center">پشت کارت ملی</h3>
            <div
              className={`relative min-h-[280px] flex flex-col items-center justify-center rounded-lg border-2 border-dashed p-4 transition-all ${
                dragActiveBack
                  ? "border-[#4899EB] bg-[#4899EB]/10"
                  : authData.nationalCardBackImage
                  ? "border-green-500 bg-green-500/5"
                  : "border-gray-600 bg-[#2A2D35]/30"
              }`}
              onDragEnter={(e) => handleDrag(e, 'back')}
              onDragLeave={(e) => handleDrag(e, 'back')}
              onDragOver={(e) => handleDrag(e, 'back')}
              onDrop={(e) => handleDrop(e, 'back')}
            >
              {authData.nationalCardBackImage ? (
                <div className="flex flex-col items-center">
                  <img
                    src={authData.nationalCardBackImage}
                    alt="پشت کارت ملی"
                    className="max-h-[200px] object-contain rounded-lg shadow-lg mb-3"
                  />

                  {/* Upload Progress */}
                  {authData.uploadProgress.id_back < 100 && (
                    <div className="w-full mb-3">
                      <div className="flex justify-between text-xs text-gray-400 mb-1">
                        <span>در حال آپلود...</span>
                        <span>{authData.uploadProgress.id_back}%</span>
                      </div>
                      <div className="w-full bg-gray-700 rounded-full h-2">
                        <div
                          className="bg-[#4899EB] h-2 rounded-full transition-all duration-300"
                          style={{ width: `${authData.uploadProgress.id_back}%` }}
                        ></div>
                      </div>
                    </div>
                  )}

                  {/* Upload Status */}
                  {authData.uploadStatus.id_back ? (
                    <p className="text-green-400 text-sm flex items-center mb-2">
                      <svg className="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      آپلود موفق
                    </p>
                  ) : (
                    <p className="text-yellow-400 text-sm flex items-center mb-2">
                      <svg className="w-4 h-4 mr-2 animate-spin" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      در حال آپلود
                    </p>
                  )}

                  <button
                    onClick={() => updateAuthData({
                      nationalCardBackImage: null,
                      uploadStatus: { ...authData.uploadStatus, id_back: false },
                      uploadProgress: { ...authData.uploadProgress, id_back: 0 }
                    })}
                    className="text-red-400 text-xs hover:text-red-300 transition-colors"
                    disabled={authData.uploadProgress.id_back > 0 && authData.uploadProgress.id_back < 100}
                  >
                    حذف و آپلود مجدد
                  </button>
                </div>
              ) : (
                <div className="text-center">
                  <div className="w-12 h-12 mx-auto mb-3 bg-[#4899EB]/20 rounded-full flex items-center justify-center">
                    <svg className="w-6 h-6 text-[#4899EB]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                    </svg>
                  </div>
                  <p className="text-white text-sm font-semibold mb-2">پشت کارت ملی</p>
                  <p className="text-white/70 text-xs text-center mb-4">
                    تصویر پشت کارت ملی را آپلود کنید
                  </p>
                  <div className="flex flex-col gap-2">
                    <button
                      onClick={() => handleUploadClick('back')}
                      className="flex items-center justify-center px-3 py-2 bg-[#4899EB] hover:bg-blue-600 rounded text-xs transition-colors"
                    >
                      انتخاب فایل
                    </button>
                    <button
                      onClick={() => handleCameraCapture('back')}
                      className="flex items-center justify-center px-3 py-2 bg-[#2A2D35] hover:bg-gray-600 border border-gray-600 rounded text-xs transition-colors"
                    >
                      دوربین
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Hidden File Inputs */}
        <input
          type="file"
          ref={frontFileInputRef}
          onChange={(e) => handleInputChange(e, 'front')}
          accept="image/*"
          capture={isMobile ? "environment" : undefined}
          className="hidden"
        />
        <input
          type="file"
          ref={backFileInputRef}
          onChange={(e) => handleInputChange(e, 'back')}
          accept="image/*"
          capture={isMobile ? "environment" : undefined}
          className="hidden"
        />

        {/* Navigation Buttons */}
        <div className="flex justify-between">
          <button
            onClick={onBack}
            className="px-6 py-3 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors"
          >
            بازگشت
          </button>
          <button
            onClick={handleNext}
            disabled={!authData.uploadStatus.id || !authData.uploadStatus.id_back}
            className="px-6 py-3 bg-[#4899EB] text-white rounded-lg hover:bg-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
          >
            {(!authData.uploadStatus.id || !authData.uploadStatus.id_back) && (
              <svg className="w-4 h-4 mr-2 animate-spin" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            )}
            {(!authData.uploadStatus.id || !authData.uploadStatus.id_back) ? 'در حال آپلود...' : 'مرحله بعد'}
          </button>
        </div>
      </div>

      {/* Modal */}
      {showModal && <Modal />}
    </div>
  );
};

export default NationalCardStep;
