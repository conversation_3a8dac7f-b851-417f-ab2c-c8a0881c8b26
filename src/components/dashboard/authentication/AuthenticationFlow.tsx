"use client";
import React, { useState, useEffect } from "react";
import PersonalInfoStep from "./steps/PersonalInfoStep";
import NationalCardStep from "./steps/NationalCardStep";
import SelfieStep from "./steps/SelfieStep";
import ConsentStep from "./steps/ConsentStep";
import ReviewStep from "./steps/ReviewStep";
import { getVerificationStatus } from "../../../requests/dashboardRequest";
import toast from "react-hot-toast";

interface AuthData {
  birthDate: string;
  acceptedTerms: boolean;
  nationalCardFrontImage: string | null;
  nationalCardBackImage: string | null;
  selfieImage: string | null;
  consentAccepted: boolean;
  signature: string | null;
  consentPdf: string | null;
  // Upload status tracking
  uploadStatus: {
    id: boolean;
    id_back: boolean;
    selfie: boolean;
    consent: boolean;
  };
  uploadProgress: {
    id: number;
    id_back: number;
    selfie: number;
    consent: number;
  };
  // Verification status from server
  verificationStatus: {
    overall_status: string;
    is_fully_verified: boolean;
    completion_percentage: number;
    document_status: {
      [key: string]: {
        id: number;
        status: 'pending' | 'approved' | 'rejected';
        description: string | null;
        created_at: string;
        updated_at: string;
      };
    };
    has_pending: boolean;
    has_rejected: boolean;
  } | null;
}

interface AuthenticationFlowProps {
  initialVerificationStatus?: any;
  initialStep?: number;
}

const AuthenticationFlow: React.FC<AuthenticationFlowProps> = ({
  initialVerificationStatus,
  initialStep = 0
}) => {
  const [currentStep, setCurrentStep] = useState(initialStep);
  const [isLoading, setIsLoading] = useState(true);

  // به‌روزرسانی مرحله جاری وقتی initialStep تغییر می‌کند
  useEffect(() => {
    setCurrentStep(initialStep);
  }, [initialStep]);
  const [authData, setAuthData] = useState<AuthData>({
    birthDate: "",
    acceptedTerms: false,
    nationalCardFrontImage: null,
    nationalCardBackImage: null,
    selfieImage: null,
    consentAccepted: false,
    signature: null,
    consentPdf: null,
    uploadStatus: {
      id: false,
      id_back: false,
      selfie: false,
      consent: false,
    },
    uploadProgress: {
      id: 0,
      id_back: 0,
      selfie: 0,
      consent: 0,
    },
    verificationStatus: initialVerificationStatus || null,
  });

  // Load verification status on component mount (only if not provided)
  useEffect(() => {
    const loadVerificationStatus = async () => {
      // If verification status is already provided, don't fetch again
      if (initialVerificationStatus) {
        setIsLoading(false);
        return;
      }

      setIsLoading(true);
      try {
        const result = await getVerificationStatus();
        if (!result.isError && result.data) {
          setAuthData(prev => ({
            ...prev,
            verificationStatus: result.data
          }));

          // If user is fully verified, show completion message
          if (result.data.is_fully_verified) {
            toast.success("احراز هویت شما با موفقیت تکمیل شده است");
          }
        }
      } catch (error) {
        console.error("Error loading verification status:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadVerificationStatus();
  }, [initialVerificationStatus]);

  const steps = [
    {
      title: "اطلاعات شخصی",
      description: "تاریخ تولد و تایید قوانین",
      component: PersonalInfoStep,
    },
    {
      title: "کارت ملی",
      description: "ارسال تصویر کارت ملی",
      component: NationalCardStep,
    },
    {
      title: "عکس سلفی",
      description: "عکس سلفی با کارت ملی",
      component: SelfieStep,
    },
    {
      title: "رضایت‌نامه",
      description: "تایید رضایت‌نامه",
      component: ConsentStep,
    },
    {
      title: "بررسی نهایی",
      description: "بررسی و ارسال اطلاعات",
      component: ReviewStep,
    },
  ];

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const updateAuthData = (data: Partial<AuthData>) => {
    setAuthData(prev => ({ ...prev, ...data }));
  };

  const canProceedToNext = (): boolean => {
    const isDocumentApproved = (docType: string): boolean => {
      return authData.verificationStatus?.document_status?.[docType]?.status === 'approved';
    };

    const isDocumentRejected = (docType: string): boolean => {
      return authData.verificationStatus?.document_status?.[docType]?.status === 'rejected';
    };

    switch (currentStep) {
      case 0: // Personal Info
        return !!(authData.birthDate && authData.acceptedTerms);
      case 1: // National Card
        return !!(
          (isDocumentApproved('id') || (authData.nationalCardFrontImage && authData.uploadStatus.id)) &&
          (isDocumentApproved('id_back') || (authData.nationalCardBackImage && authData.uploadStatus.id_back))
        );
      case 2: // Selfie
        return !!(isDocumentApproved('selfie') || (authData.selfieImage && authData.uploadStatus.selfie));
      case 3: // Consent
        return !!(isDocumentApproved('consent') || (authData.consentAccepted &&
               authData.signature &&
               authData.consentPdf &&
               authData.uploadStatus.consent));
      default:
        return false;
    }
  };

  const CurrentStepComponent = steps[currentStep].component;

  // Show loading state while fetching verification status
  if (isLoading) {
    return (
      <div className="min-h-screen bg-[#141416] text-white flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 mx-auto mb-4 border-4 border-[#4899EB] border-t-transparent rounded-full animate-spin"></div>
          <p className="text-gray-400">در حال بارگذاری وضعیت احراز هویت...</p>
        </div>
      </div>
    );
  }

  // Show verification status if documents are submitted (but allow re-upload for rejected documents)
  if (authData.verificationStatus && !authData.verificationStatus.has_rejected) {
    const { verificationStatus } = authData;

    // If fully verified, show success message
    if (verificationStatus.is_fully_verified) {
      return (
        <div className="min-h-screen bg-[#141416] text-white">
          <div className="max-w-4xl mx-auto p-6">
            <div className="bg-[#18191D] rounded-lg p-8 text-center">
              <div className="w-24 h-24 mx-auto mb-6 bg-green-500/20 rounded-full flex items-center justify-center">
                <svg className="w-12 h-12 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
              <h2 className="text-2xl font-bold text-green-500 mb-4">احراز هویت تایید شده</h2>
              <p className="text-gray-400 mb-6">
                احراز هویت شما با موفقیت تایید شده است. اکنون می‌توانید از تمام خدمات اکسچنجیم استفاده کنید.
              </p>
              <button
                onClick={() => window.location.href = '/dashboard'}
                className="bg-[#4899EB] hover:bg-blue-600 text-white font-semibold px-8 py-3 rounded-lg transition-colors"
              >
                بازگشت به داشبورد
              </button>
            </div>
          </div>
        </div>
      );
    }

    // If has pending or rejected documents, show status
    if (verificationStatus.has_pending || verificationStatus.has_rejected) {
      return (
        <div className="min-h-screen bg-[#141416] text-white">
          <div className="max-w-4xl mx-auto p-6">
            <div className="bg-[#18191D] rounded-lg p-8">
              <h2 className="text-2xl font-bold mb-6 text-center">وضعیت احراز هویت</h2>

              <div className="space-y-4 mb-8">
                {Object.entries(verificationStatus.document_status).map(([docType, status]) => {
                  const docNames: { [key: string]: string } = {
                    id: 'جلوی کارت ملی',
                    id_back: 'پشت کارت ملی',
                    selfie: 'عکس سلفی',
                    consent: 'رضایت‌نامه'
                  };

                  return (
                    <div key={docType} className="flex items-center justify-between p-4 bg-[#2A2D35] rounded-lg">
                      <span className="text-white">{docNames[docType]}</span>
                      <div className="flex items-center">
                        {status.status === 'approved' && (
                          <span className="text-green-500 flex items-center">
                            <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                            تایید شده
                          </span>
                        )}
                        {status.status === 'pending' && (
                          <span className="text-yellow-500 flex items-center">
                            <svg className="w-5 h-5 mr-2 animate-spin" fill="none" viewBox="0 0 24 24">
                              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                              <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                            در حال بررسی
                          </span>
                        )}
                        {status.status === 'rejected' && (
                          <span className="text-red-500 flex items-center">
                            <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                            </svg>
                            رد شده
                          </span>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>

              {verificationStatus.has_rejected && (
                <div className="text-center">
                  <p className="text-red-400 mb-4">
                    برخی از مدارک شما رد شده است. لطفا مدارک رد شده را مجدداً ارسال کنید.
                  </p>
                  <button
                    onClick={() => {
                      setAuthData(prev => ({ ...prev, verificationStatus: null }));
                      setCurrentStep(0);
                    }}
                    className="bg-[#4899EB] hover:bg-blue-600 text-white font-semibold px-8 py-3 rounded-lg transition-colors"
                  >
                    ارسال مجدد مدارک
                  </button>
                </div>
              )}

              {verificationStatus.has_pending && !verificationStatus.has_rejected && (
                <div className="text-center">
                  <p className="text-yellow-400 mb-4">
                    مدارک شما در حال بررسی است. لطفا منتظر بمانید.
                  </p>
                  <button
                    onClick={() => window.location.href = '/dashboard'}
                    className="bg-gray-700 hover:bg-gray-600 text-white font-semibold px-8 py-3 rounded-lg transition-colors"
                  >
                    بازگشت به داشبورد
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      );
    }
  }

  return (
    <div className="min-h-screen bg-[#141416] text-white">
      {/* Progress Header */}
      <div className="bg-[#18191D] border-b border-gray-700 p-6">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-2xl font-bold mb-6 text-center">احراز هویت</h1>

          {/* Progress Steps */}
          <div className="flex items-center justify-between mb-4">
            {steps.map((step, index) => (
              <div key={index} className="flex items-center">
                <div className="flex flex-col items-center">
                  <div
                    className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium ${
                      index < currentStep
                        ? "bg-green-500 text-white"
                        : index === currentStep
                        ? "bg-[#4899EB] text-white"
                        : "bg-gray-600 text-gray-300"
                    }`}
                  >
                    {index < currentStep ? (
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    ) : (
                      index + 1
                    )}
                  </div>
                  <div className="mt-2 text-center">
                    <p className="text-xs font-medium">{step.title}</p>
                    <p className="text-xs text-gray-400">{step.description}</p>
                  </div>
                </div>
                {index < steps.length - 1 && (
                  <div
                    className={`flex-1 h-0.5 mx-4 ${
                      index < currentStep ? "bg-green-500" : "bg-gray-600"
                    }`}
                  />
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Step Content */}
      <div className="max-w-4xl mx-auto p-6">
        <CurrentStepComponent
          authData={authData}
          updateAuthData={updateAuthData}
          onNext={handleNext}
          onBack={handleBack}
          isFirstStep={currentStep === 0}
          isLastStep={currentStep === steps.length - 1}
          canProceedToNext={canProceedToNext()}
        />
      </div>
    </div>
  );
};

export default AuthenticationFlow;
