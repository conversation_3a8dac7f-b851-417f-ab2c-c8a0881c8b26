"use client";
import React, { useState, useEffect } from "react";
import PersonalInfoStep from "./steps/PersonalInfoStep";
import NationalCardStep from "./steps/NationalCardStep";
import SelfieStep from "./steps/SelfieStep";
import ConsentStep from "./steps/ConsentStep";
import ReviewStep from "./steps/ReviewStep";
import { getVerificationStatus } from "../../../requests/dashboardRequest";
import toast from "react-hot-toast";

interface AuthData {
  birthDate: string;
  acceptedTerms: boolean;
  nationalCardFrontImage: string | null;
  nationalCardBackImage: string | null;
  selfieImage: string | null;
  consentAccepted: boolean;
  signature: string | null;
  consentPdf: string | null;
  // Upload status tracking
  uploadStatus: {
    id: boolean;
    id_back: boolean;
    selfie: boolean;
    consent: boolean;
  };
  uploadProgress: {
    id: number;
    id_back: number;
    selfie: number;
    consent: number;
  };
  // Verification status from server
  verificationStatus: {
    overall_status: string;
    is_fully_verified: boolean;
    completion_percentage: number;
    document_status: {
      [key: string]: {
        id: number;
        status: 'pending' | 'approved' | 'rejected';
        description: string | null;
        created_at: string;
        updated_at: string;
      };
    };
    has_pending: boolean;
    has_rejected: boolean;
  } | null;
}

const AuthenticationFlow = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [authData, setAuthData] = useState<AuthData>({
    birthDate: "",
    acceptedTerms: false,
    nationalCardFrontImage: null,
    nationalCardBackImage: null,
    selfieImage: null,
    consentAccepted: false,
    signature: null,
    consentPdf: null,
    uploadStatus: {
      id: false,
      id_back: false,
      selfie: false,
      consent: false,
    },
    uploadProgress: {
      id: 0,
      id_back: 0,
      selfie: 0,
      consent: 0,
    },
    verificationStatus: null,
  });

  // Load verification status on component mount
  useEffect(() => {
    const loadVerificationStatus = async () => {
      setIsLoading(true);
      try {
        const result = await getVerificationStatus();
        if (!result.isError && result.data) {
          setAuthData(prev => ({
            ...prev,
            verificationStatus: result.data
          }));

          // If user is fully verified, show completion message
          if (result.data.is_fully_verified) {
            toast.success("احراز هویت شما با موفقیت تکمیل شده است");
          }
        }
      } catch (error) {
        console.error("Error loading verification status:", error);
      } finally {
        setIsLoading(false);
      }
    };

    loadVerificationStatus();
  }, []);

  const steps = [
    {
      title: "اطلاعات شخصی",
      description: "تاریخ تولد و تایید قوانین",
      component: PersonalInfoStep,
    },
    {
      title: "کارت ملی",
      description: "ارسال تصویر کارت ملی",
      component: NationalCardStep,
    },
    {
      title: "عکس سلفی",
      description: "عکس سلفی با کارت ملی",
      component: SelfieStep,
    },
    {
      title: "رضایت‌نامه",
      description: "تایید رضایت‌نامه",
      component: ConsentStep,
    },
    {
      title: "بررسی نهایی",
      description: "بررسی و ارسال اطلاعات",
      component: ReviewStep,
    },
  ];

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handleBack = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const updateAuthData = (data: Partial<AuthData>) => {
    setAuthData(prev => ({ ...prev, ...data }));
  };

  const canProceedToNext = () => {
    switch (currentStep) {
      case 0: // Personal Info
        return authData.birthDate && authData.acceptedTerms;
      case 1: // National Card
        return authData.nationalCardFrontImage &&
               authData.nationalCardBackImage &&
               authData.uploadStatus.id &&
               authData.uploadStatus.id_back;
      case 2: // Selfie
        return authData.selfieImage && authData.uploadStatus.selfie;
      case 3: // Consent
        return authData.consentAccepted &&
               authData.signature &&
               authData.consentPdf &&
               authData.uploadStatus.consent;
      default:
        return false;
    }
  };

  const CurrentStepComponent = steps[currentStep].component;

  return (
    <div className="min-h-screen bg-[#141416] text-white">
      {/* Progress Header */}
      <div className="bg-[#18191D] border-b border-gray-700 p-6">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-2xl font-bold mb-6 text-center">احراز هویت</h1>

          {/* Progress Steps */}
          <div className="flex items-center justify-between mb-4">
            {steps.map((step, index) => (
              <div key={index} className="flex items-center">
                <div className="flex flex-col items-center">
                  <div
                    className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium ${
                      index < currentStep
                        ? "bg-green-500 text-white"
                        : index === currentStep
                        ? "bg-[#4899EB] text-white"
                        : "bg-gray-600 text-gray-300"
                    }`}
                  >
                    {index < currentStep ? (
                      <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    ) : (
                      index + 1
                    )}
                  </div>
                  <div className="mt-2 text-center">
                    <p className="text-xs font-medium">{step.title}</p>
                    <p className="text-xs text-gray-400">{step.description}</p>
                  </div>
                </div>
                {index < steps.length - 1 && (
                  <div
                    className={`flex-1 h-0.5 mx-4 ${
                      index < currentStep ? "bg-green-500" : "bg-gray-600"
                    }`}
                  />
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Step Content */}
      <div className="max-w-4xl mx-auto p-6">
        <CurrentStepComponent
          authData={authData}
          updateAuthData={updateAuthData}
          onNext={handleNext}
          onBack={handleBack}
          isFirstStep={currentStep === 0}
          isLastStep={currentStep === steps.length - 1}
          canProceedToNext={canProceedToNext()}
        />
      </div>
    </div>
  );
};

export default AuthenticationFlow;
