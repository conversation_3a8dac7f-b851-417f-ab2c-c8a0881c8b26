"use client";
import React, { useState } from "react";
import AuthenticationFlow from "@/components/dashboard/authentication/AuthenticationFlow";

const Page = () => {
  const [showFlow, setShowFlow] = useState(false);

  if (showFlow) {
    return <AuthenticationFlow />;
  }

  const steps = [
    {
      title: "اطلاعات شخصی",
      description: "تاریخ تولد و تایید قوانین",
      icon: "👤",
    },
    {
      title: "کارت ملی",
      description: "ارسال تصویر کارت ملی",
      icon: "🆔",
    },
    {
      title: "عکس سلفی",
      description: "عکس سلفی با کارت ملی",
      icon: "🤳",
    },
    {
      title: "رضایت‌نامه",
      description: "تایید رضایت‌نامه",
      icon: "📝",
    },
  ];

  return (
    <div className="min-h-screen bg-[#141416] text-white p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-3xl font-bold mb-4">احراز هویت</h1>
          <p className="text-gray-400 text-lg">
            برای دسترسی کامل به خدمات اکسچنجیم، لطفا مراحل احراز هویت را تکمیل کنید
          </p>
        </div>

        {/* Steps Overview */}
        <div className="bg-[#18191D] rounded-lg p-8 mb-8">
          <h2 className="text-2xl font-bold text-center mb-8">مراحل احراز هویت</h2>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            {steps.map((step, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 bg-[#4899EB]/20 rounded-full flex items-center justify-center text-2xl">
                  {step.icon}
                </div>
                <h3 className="font-semibold mb-2">{step.title}</h3>
                <p className="text-gray-400 text-sm">{step.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Benefits */}
        <div className="bg-[#18191D] rounded-lg p-8 mb-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
            <div>
              <h2 className="text-2xl font-bold mb-4">مزایای سطح طلایی</h2>
              <p className="text-gray-400 mb-6">
                پس از تکمیل احراز هویت، به سطح طلایی ارتقا یافته و از مزایای زیر بهره‌مند شوید:
              </p>
              <ul className="space-y-3">
                <li className="flex items-center">
                  <svg className="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  خرید و فروش نامحدود
                </li>
                <li className="flex items-center">
                  <svg className="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  واریز و برداشت نامحدود
                </li>
                <li className="flex items-center">
                  <svg className="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  دسترسی به تمام ویژگی‌ها
                </li>
                <li className="flex items-center">
                  <svg className="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  پشتیبانی اولویت‌دار
                </li>
              </ul>
            </div>
            <div className="text-center">
              <div className="w-48 h-48 mx-auto bg-gradient-to-br from-yellow-400/20 to-yellow-600/20 rounded-full flex items-center justify-center mb-4">
                <span className="text-6xl">🏆</span>
              </div>
              <h3 className="text-xl font-bold text-yellow-400">سطح طلایی</h3>
            </div>
          </div>
        </div>

        {/* Instructions */}
        <div className="bg-[#18191D] rounded-lg p-8 mb-8">
          <h2 className="text-2xl font-bold mb-6">نکات مهم</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-start">
                <svg className="w-5 h-5 text-[#4899EB] mt-1 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
                <p className="text-gray-300">از مدارک جدید و معتبر استفاده کنید</p>
              </div>
              <div className="flex items-start">
                <svg className="w-5 h-5 text-[#4899EB] mt-1 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
                <p className="text-gray-300">تصاویر باید واضح و خوانا باشند</p>
              </div>
            </div>
            <div className="space-y-4">
              <div className="flex items-start">
                <svg className="w-5 h-5 text-[#4899EB] mt-1 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
                <p className="text-gray-300">از نور مناسب برای عکس‌برداری استفاده کنید</p>
              </div>
              <div className="flex items-start">
                <svg className="w-5 h-5 text-[#4899EB] mt-1 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
                <p className="text-gray-300">زمان بررسی معمولاً 24-72 ساعت است</p>
              </div>
            </div>
          </div>
        </div>

        {/* Start Button */}
        <div className="text-center">
          <button
            onClick={() => setShowFlow(true)}
            className="bg-[#4899EB] hover:bg-blue-600 text-white font-semibold px-8 py-4 rounded-lg text-lg transition-colors"
          >
            شروع احراز هویت
          </button>
        </div>
      </div>
    </div>
  );
};

export default Page;
