"use client";
import React, { useState, useEffect, useCallback } from "react";
import AuthenticationFlow from "@/components/dashboard/authentication/AuthenticationFlow";
import { getVerificationStatus } from "@/requests/dashboardRequest";
import toast from "react-hot-toast";

const Page = () => {
  const [showFlow, setShowFlow] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [verificationStatus, setVerificationStatus] = useState<any>(null);
  const [targetStep, setTargetStep] = useState<number>(0);
  const [forceRender, setForceRender] = useState(0);

  // تابع برای handle کردن re-upload
  const handleReUpload = useCallback((docType: string) => {
    console.log("Re-upload button clicked for:", docType);

    // تعیین مرحله بر اساس نوع مدرک
    let step = 0;
    switch (docType) {
      case 'id':
      case 'id_back':
        step = 1; // مرحله کارت ملی
        break;
      case 'selfie':
        step = 2; // مرحله سلفی
        break;
      case 'consent':
        step = 3; // مرحله رضایت‌نامه
        break;
      default:
        step = 0;
    }

    console.log("Going to step:", step);

    // روش جایگزین: استفاده از URL parameter
    const url = new URL(window.location.href);
    url.searchParams.set('step', step.toString());
    url.searchParams.set('reupload', 'true');
    window.location.href = url.toString();
  }, []);

  // بررسی وضعیت احراز هویت هنگام ورود به صفحه
  useEffect(() => {
    const checkVerificationStatus = async () => {
      setIsLoading(true);

      // بررسی URL parameters برای re-upload
      const urlParams = new URLSearchParams(window.location.search);
      const stepParam = urlParams.get('step');
      const reuploadParam = urlParams.get('reupload');

      if (reuploadParam === 'true' && stepParam) {
        console.log("Re-upload detected from URL, going to step:", stepParam);
        setTargetStep(parseInt(stepParam));
        setShowFlow(true);
        // پاک کردن parameters از URL
        const url = new URL(window.location.href);
        url.searchParams.delete('step');
        url.searchParams.delete('reupload');
        window.history.replaceState({}, '', url.toString());
        setIsLoading(false);
        return;
      }

      try {
        const result = await getVerificationStatus();
        if (!result.isError && result.data) {
          console.log("Verification Status Data:", result.data);
          console.log("is_fully_verified:", result.data.is_fully_verified);
          console.log("has_pending:", result.data.has_pending);
          console.log("has_rejected:", result.data.has_rejected);

          setVerificationStatus(result.data);

          // اگر کاربر کاملاً احراز هویت شده، پیام موفقیت نشان بده
          if (result.data.is_fully_verified) {
            console.log("User is fully verified, showing success toast");
            toast.success("احراز هویت شما تایید شده است");
          }
          // اگر مدارک در حال بررسی هستند
          else if (result.data.has_pending && !result.data.has_rejected) {
            console.log("Documents are pending");
            toast("مدارک شما در حال بررسی است", { icon: "⏳" });
          }
          // اگر مدارک رد شده‌اند
          else if (result.data.has_rejected) {
            console.log("Some documents are rejected");
            toast.error("برخی از مدارک شما رد شده است. لطفا مجدداً ارسال کنید");
          }
          else {
            console.log("No specific condition met, checking document statuses manually");
            // بررسی دستی وضعیت مدارک
            const allApproved = Object.values(result.data.document_status || {})
              .every((doc: any) => doc.status === 'approved');
            console.log("All documents approved (manual check):", allApproved);

            if (allApproved && Object.keys(result.data.document_status || {}).length > 0) {
              console.log("All documents are approved manually, showing success toast");
              toast.success("احراز هویت شما تایید شده است");
            }
          }
        }
      } catch (error) {
        console.error("Error checking verification status:", error);
      } finally {
        setIsLoading(false);
      }
    };

    checkVerificationStatus();
  }, []);

  // نمایش صفحه بارگذاری
  if (isLoading) {
    return (
      <div className="min-h-screen bg-[#141416] text-white flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 mx-auto mb-4 border-4 border-[#4899EB] border-t-transparent rounded-full animate-spin"></div>
          <p className="text-gray-400">در حال بررسی وضعیت احراز هویت...</p>
        </div>
      </div>
    );
  }

  // اگر کاربر کاملاً احراز هویت شده
  if (verificationStatus?.is_fully_verified) {
    return (
      <div className="min-h-screen bg-[#141416] text-white">
        <div className="max-w-4xl mx-auto p-6">
          <div className="bg-[#18191D] rounded-lg p-8 text-center">
            <div className="w-24 h-24 mx-auto mb-6 bg-green-500/20 rounded-full flex items-center justify-center">
              <svg className="w-12 h-12 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
              </svg>
            </div>
            <h2 className="text-2xl font-bold text-green-500 mb-4">احراز هویت تایید شده</h2>
            <p className="text-gray-400 mb-6">
              احراز هویت شما با موفقیت تایید شده است. اکنون می‌توانید از تمام خدمات اکسچنجیم استفاده کنید.
            </p>
            <button
              onClick={() => window.location.href = '/dashboard'}
              className="bg-[#4899EB] hover:bg-blue-600 text-white font-semibold px-8 py-3 rounded-lg transition-colors"
            >
              بازگشت به داشبورد
            </button>
          </div>
        </div>
      </div>
    );
  }

  // اگر مدارک در حال بررسی یا رد شده‌اند
  if (verificationStatus && (verificationStatus.has_pending || verificationStatus.has_rejected)) {
    return (
      <div className="min-h-screen bg-[#141416] text-white">
        <div className="max-w-4xl mx-auto p-6">
          <div className="bg-[#18191D] rounded-lg p-8">
            <h2 className="text-2xl font-bold mb-6 text-center">وضعیت احراز هویت</h2>

            <div className="space-y-4 mb-8">
              {Object.entries(verificationStatus.document_status).map(([docType, status]: [string, any]) => {
                const docNames: { [key: string]: string } = {
                  id: 'جلوی کارت ملی',
                  id_back: 'پشت کارت ملی',
                  selfie: 'عکس سلفی',
                  consent: 'رضایت‌نامه'
                };

                return (
                  <div key={docType} className="flex items-center justify-between p-4 bg-[#2A2D35] rounded-lg">
                    <span className="text-white">{docNames[docType]}</span>
                    <div className="flex items-center">
                      {status.status === 'approved' && (
                        <span className="text-green-500 flex items-center">
                          <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                          تایید شده
                        </span>
                      )}
                      {status.status === 'pending' && (
                        <span className="text-yellow-500 flex items-center">
                          <svg className="w-5 h-5 mr-2 animate-spin" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                          در حال بررسی
                        </span>
                      )}
                      {status.status === 'rejected' && (
                        <div className="flex flex-col gap-2">
                          <div className="flex items-center gap-3">
                            <span className="text-red-500 flex items-center">
                              <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                              </svg>
                              رد شده
                            </span>
                            <button
                              onClick={(e) => {
                                e.preventDefault();
                                e.stopPropagation();
                                handleReUpload(docType);
                              }}
                              className="bg-[#4899EB] hover:bg-blue-600 text-white text-xs px-3 py-1 rounded transition-colors"
                            >
                              ارسال مجدد
                            </button>
                          </div>
                          {status.description && (
                            <div className="bg-red-500/10 border border-red-500/20 rounded p-2 mt-1">
                              <p className="text-red-300 text-xs">
                                <strong>دلیل رد:</strong> {status.description}
                              </p>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>



            {verificationStatus.has_pending && !verificationStatus.has_rejected && (
              <div className="text-center">
                <p className="text-yellow-400 mb-4">
                  مدارک شما در حال بررسی است. لطفا منتظر بمانید.
                </p>
                <div className="space-y-3">
                  <button
                    onClick={() => window.location.href = '/dashboard'}
                    className="bg-gray-700 hover:bg-gray-600 text-white font-semibold px-8 py-3 rounded-lg transition-colors"
                  >
                    بازگشت به داشبورد
                  </button>

                
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  }

  console.log("Current showFlow state:", showFlow);
  console.log("Current targetStep:", targetStep);

  if (showFlow) {
    console.log("Rendering AuthenticationFlow with step:", targetStep);
    return <AuthenticationFlow initialVerificationStatus={verificationStatus} initialStep={targetStep} />;
  }

  const steps = [
    {
      title: "اطلاعات شخصی",
      description: "تاریخ تولد و تایید قوانین",
      icon: "👤",
    },
    {
      title: "کارت ملی",
      description: "ارسال تصویر کارت ملی",
      icon: "🆔",
    },
    {
      title: "عکس سلفی",
      description: "عکس سلفی با کارت ملی",
      icon: "🤳",
    },
    {
      title: "رضایت‌نامه",
      description: "تایید رضایت‌نامه",
      icon: "📝",
    },
  ];

  return (
    <div className="min-h-screen bg-[#141416] text-white p-6">
      <div className="max-w-6xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-3xl font-bold mb-4">احراز هویت</h1>
          <p className="text-gray-400 text-lg">
            برای دسترسی کامل به خدمات اکسچنجیم، لطفا مراحل احراز هویت را تکمیل کنید
          </p>
        </div>

        {/* Steps Overview */}
        <div className="bg-[#18191D] rounded-lg p-8 mb-8">
          <h2 className="text-2xl font-bold text-center mb-8">مراحل احراز هویت</h2>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            {steps.map((step, index) => (
              <div key={index} className="text-center">
                <div className="w-16 h-16 mx-auto mb-4 bg-[#4899EB]/20 rounded-full flex items-center justify-center text-2xl">
                  {step.icon}
                </div>
                <h3 className="font-semibold mb-2">{step.title}</h3>
                <p className="text-gray-400 text-sm">{step.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Benefits */}
        <div className="bg-[#18191D] rounded-lg p-8 mb-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
            <div>
              <h2 className="text-2xl font-bold mb-4">مزایای سطح طلایی</h2>
              <p className="text-gray-400 mb-6">
                پس از تکمیل احراز هویت، به سطح طلایی ارتقا یافته و از مزایای زیر بهره‌مند شوید:
              </p>
              <ul className="space-y-3">
                <li className="flex items-center">
                  <svg className="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  خرید و فروش نامحدود
                </li>
                <li className="flex items-center">
                  <svg className="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  واریز و برداشت نامحدود
                </li>
                <li className="flex items-center">
                  <svg className="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  دسترسی به تمام ویژگی‌ها
                </li>
                <li className="flex items-center">
                  <svg className="w-5 h-5 text-green-500 mr-3" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                  پشتیبانی اولویت‌دار
                </li>
              </ul>
            </div>
            <div className="text-center">
              <div className="w-48 h-48 mx-auto bg-gradient-to-br from-yellow-400/20 to-yellow-600/20 rounded-full flex items-center justify-center mb-4">
                <span className="text-6xl">🏆</span>
              </div>
              <h3 className="text-xl font-bold text-yellow-400">سطح طلایی</h3>
            </div>
          </div>
        </div>

        {/* Instructions */}
        <div className="bg-[#18191D] rounded-lg p-8 mb-8">
          <h2 className="text-2xl font-bold mb-6">نکات مهم</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <div className="flex items-start">
                <svg className="w-5 h-5 text-[#4899EB] mt-1 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
                <p className="text-gray-300">از مدارک جدید و معتبر استفاده کنید</p>
              </div>
              <div className="flex items-start">
                <svg className="w-5 h-5 text-[#4899EB] mt-1 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
                <p className="text-gray-300">تصاویر باید واضح و خوانا باشند</p>
              </div>
            </div>
            <div className="space-y-4">
              <div className="flex items-start">
                <svg className="w-5 h-5 text-[#4899EB] mt-1 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
                <p className="text-gray-300">از نور مناسب برای عکس‌برداری استفاده کنید</p>
              </div>
              <div className="flex items-start">
                <svg className="w-5 h-5 text-[#4899EB] mt-1 mr-3 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                </svg>
                <p className="text-gray-300">زمان بررسی معمولاً 24-72 ساعت است</p>
              </div>
            </div>
          </div>
        </div>

        {/* Start Button */}
        <div className="text-center">
          <button
            onClick={() => {
              setTargetStep(0); // شروع از مرحله اول
              setShowFlow(true);
            }}
            className="bg-[#4899EB] hover:bg-blue-600 text-white font-semibold px-8 py-4 rounded-lg text-lg transition-colors"
          >
            شروع احراز هویت
          </button>
        </div>
      </div>
    </div>
  );
};

export default Page;
